version: "3"
services:
  sapgoodsmvtcreate:
    build: .
    image: sap/sapgoodsmvtcreate
    container_name: sapgoodsmvtcreate
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 3G
        reservations:
          cpus: '0.25'
          memory: 2G
    ports:
      - "5790:3000"
    env_file:
      - "./config/config.env"
    environment:
      - PORT=5790
      - SAPSYSID=ND1
      - ASHOST=ud1-sv-saped1.matw.matthewsintl.com
      - SYSNR=00
      - CLIENT=310
      - LANGU=EN
      - RFC_TRACE=0
      - JWTLINK=http://c3mft01ld.amer.schawk.com:8081/auth/.well-known/jwks.json
      - JWTLOGIN=http://c3mft01ld.amer.schawk.com:8081/auth/login
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
