{"name": "migo", "version": "1.0.0", "description": "SAP MIGO", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js"}, "keywords": [], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@joi/date": "^2.0.1", "basic-auth": "^2.0.1", "cookie-parser": "^1.4.5", "express": "^4.17.1", "express-jwt": "^8.4.1", "express-session": "^1.17.1", "helmet": "^4.4.1", "http-errors": "^2.0.0", "joi": "^17.4.0", "jwks-rsa": "^2.0.2", "morgan": "^1.10.0", "next": "^14.2.3", "node-rfc": "^2.4.0", "xml2js": "^0.6.2"}, "devDependencies": {"dotenv": "^8.6.0", "nodemon": "^2.0.12"}}