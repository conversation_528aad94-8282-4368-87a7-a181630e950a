<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="root">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Header">
					<xs:complexType>
						<xs:sequence>
							<xs:element type="xs:string" name="GOODSMVT_CODE" />
							<xs:element type="xs:string" name="PSTNG_DATE" />
							<xs:element type="xs:string" name="DOC_DATE" />
							<xs:element type="xs:string" name="REF_DOC_NO" />
							<xs:element type="xs:string" name="BILL_OF_LADING" />
							<xs:element type="xs:string" name="GR_GI_SLIP_NO" />
							<xs:element type="xs:string" name="PR_UNAME" />
							<xs:element type="xs:string" name="HEADER_TXT" />
							<xs:element type="xs:string" name="VER_GR_GI_SLIP" />
							<xs:element type="xs:string" name="VER_GR_GI_SLIPX" />
							<xs:element type="xs:string" name="EXT_WMS" />
							<xs:element type="xs:string" name="REF_DOC_NO_LONG" />
							<xs:element type="xs:string" name="BILL_OF_LADING_LONG" />
							<xs:element type="xs:string" name="BAR_CODE" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element maxOccurs="unbounded" name="Items">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="Item">
								<xs:complexType>
									<xs:sequence>
										<xs:element type="xs:string" name="MATERIAL" />
										<xs:element type="xs:string" name="PLANT" />
										<xs:element type="xs:string" name="STGE_LOC" />
										<xs:element type="xs:string" name="BATCH" />
										<xs:element type="xs:string" name="MOVE_TYPE" />
										<xs:element type="xs:string" name="STCK_TYPE" />
										<xs:element type="xs:string" name="SPEC_STOCK" />
										<xs:element type="xs:string" name="VENDOR" />
										<xs:element type="xs:string" name="CUSTOMER" />
										<xs:element type="xs:string" name="SALES_ORD" />
										<xs:element type="xs:string" name="S_ORD_ITEM" />
										<xs:element type="xs:string" name="SCHED_LINE" />
										<xs:element type="xs:string" name="VAL_TYPE" />
										<xs:element type="xs:string" name="ENTRY_QNT" />
										<xs:element type="xs:string" name="ENTRY_UOM" />
										<xs:element type="xs:string" name="ENTRY_UOM_ISO" />
										<xs:element type="xs:string" name="PO_PR_QNT" />
										<xs:element type="xs:string" name="ORDERPR_UN" />
										<xs:element type="xs:string" name="ORDERPR_UN_ISO" />
										<xs:element type="xs:string" name="PO_NUMBER" />
										<xs:element type="xs:string" name="PO_ITEM" />
										<xs:element type="xs:string" name="SHIPPING" />
										<xs:element type="xs:string" name="COMP_SHIP" />
										<xs:element type="xs:string" name="NO_MORE_GR" />
										<xs:element type="xs:string" name="ITEM_TEXT" />
										<xs:element type="xs:string" name="GR_RCPT" />
										<xs:element type="xs:string" name="UNLOAD_PT" />
										<xs:element type="xs:string" name="COSTCENTER" />
										<xs:element type="xs:string" name="ORDERID" />
										<xs:element type="xs:string" name="ORDER_ITNO" />
										<xs:element type="xs:string" name="CALC_MOTIVE" />
										<xs:element type="xs:string" name="ASSET_NO" />
										<xs:element type="xs:string" name="SUB_NUMBER" />
										<xs:element type="xs:string" name="RESERV_NO" />
										<xs:element type="xs:string" name="RES_ITEM" />
										<xs:element type="xs:string" name="RES_TYPE" />
										<xs:element type="xs:string" name="WITHDRAWN" />
										<xs:element type="xs:string" name="MOVE_MAT" />
										<xs:element type="xs:string" name="MOVE_PLANT" />
										<xs:element type="xs:string" name="MOVE_STLOC" />
										<xs:element type="xs:string" name="MOVE_BATCH" />
										<xs:element type="xs:string" name="MOVE_VAL_TYPE" />
										<xs:element type="xs:string" name="MVT_IND" />
										<xs:element type="xs:string" name="MOVE_REAS" />
										<xs:element type="xs:string" name="RL_EST_KEY" />
										<xs:element type="xs:string" name="REF_DATE" />
										<xs:element type="xs:string" name="COST_OBJ" />
										<xs:element type="xs:string" name="PROFIT_SEGM_NO" />
										<xs:element type="xs:string" name="PROFIT_CTR" />
										<xs:element type="xs:string" name="WBS_ELEM" />
										<xs:element type="xs:string" name="NETWORK" />
										<xs:element type="xs:string" name="ACTIVITY" />
										<xs:element type="xs:string" name="PART_ACCT" />
										<xs:element type="xs:string" name="AMOUNT_LC" />
										<xs:element type="xs:string" name="AMOUNT_SV" />
										<xs:element type="xs:string" name="REF_DOC_YR" />
										<xs:element type="xs:string" name="REF_DOC" />
										<xs:element type="xs:string" name="REF_DOC_IT" />
										<xs:element type="xs:string" name="EXPIRYDATE" />
										<xs:element type="xs:string" name="PROD_DATE" />
										<xs:element type="xs:string" name="FUND" />
										<xs:element type="xs:string" name="FUNDS_CTR" />
										<xs:element type="xs:string" name="CMMT_ITEM" />
										<xs:element type="xs:string" name="VAL_SALES_ORD" />
										<xs:element type="xs:string" name="VAL_S_ORD_ITEM" />
										<xs:element type="xs:string" name="VAL_WBS_ELEM" />
										<xs:element type="xs:string" name="GL_ACCOUNT" />
										<xs:element type="xs:string" name="IND_PROPOSE_QUANX" />
										<xs:element type="xs:string" name="XSTOB" />
										<xs:element type="xs:string" name="EAN_UPC" />
										<xs:element type="xs:string" name="DELIV_NUMB_TO_SEARCH" />
										<xs:element type="xs:string" name="DELIV_ITEM_TO_SEARCH" />
										<xs:element type="xs:string" name="SERIALNO_AUTO_NUMBERASSIGNMENT" />
										<xs:element type="xs:string" name="VENDRBATCH" />
										<xs:element type="xs:string" name="STGE_TYPE" />
										<xs:element type="xs:string" name="STGE_BIN" />
										<xs:element type="xs:string" name="SU_PL_STCK_1" />
										<xs:element type="xs:string" name="ST_UN_QTYY_1" />
										<xs:element type="xs:string" name="ST_UN_QTYY_1_ISO" />
										<xs:element type="xs:string" name="UNITTYPE_1" />
										<xs:element type="xs:string" name="SU_PL_STCK_2" />
										<xs:element type="xs:string" name="ST_UN_QTYY_2" />
										<xs:element type="xs:string" name="ST_UN_QTYY_2_ISO" />
										<xs:element type="xs:string" name="UNITTYPE_2" />
										<xs:element type="xs:string" name="STGE_TYPE_PC" />
										<xs:element type="xs:string" name="STGE_BIN_PC" />
										<xs:element type="xs:string" name="NO_PST_CHGNT" />
										<xs:element type="xs:string" name="GR_NUMBER" />
										<xs:element type="xs:string" name="STGE_TYPE_ST" />
										<xs:element type="xs:string" name="STGE_BIN_ST" />
										<xs:element type="xs:string" name="MATDOC_TR_CANCEL" />
										<xs:element type="xs:string" name="MATITEM_TR_CANCEL" />
										<xs:element type="xs:string" name="MATYEAR_TR_CANCEL" />
										<xs:element type="xs:string" name="NO_TRANSFER_REQ" />
										<xs:element type="xs:string" name="CO_BUSPROC" />
										<xs:element type="xs:string" name="ACTTYPE" />
										<xs:element type="xs:string" name="SUPPL_VEND" />
										<xs:element type="xs:string" name="MATERIAL_EXTERNAL" />
										<xs:element type="xs:string" name="MATERIAL_GUID" />
										<xs:element type="xs:string" name="MATERIAL_VERSION" />
										<xs:element type="xs:string" name="MOVE_MAT_EXTERNAL" />
										<xs:element type="xs:string" name="MOVE_MAT_GUID" />
										<xs:element type="xs:string" name="MOVE_MAT_VERSION" />
										<xs:element type="xs:string" name="FUNC_AREA" />
										<xs:element type="xs:string" name="TR_PART_BA" />
										<xs:element type="xs:string" name="PAR_COMPCO" />
										<xs:element type="xs:string" name="DELIV_NUMB" />
										<xs:element type="xs:string" name="DELIV_ITEM" />
										<xs:element type="xs:string" name="NB_SLIPS" />
										<xs:element type="xs:string" name="NB_SLIPSX" />
										<xs:element type="xs:string" name="GR_RCPTX" />
										<xs:element type="xs:string" name="UNLOAD_PTX" />
										<xs:element type="xs:string" name="SPEC_MVMT" />
										<xs:element type="xs:string" name="GRANT_NBR" />
										<xs:element type="xs:string" name="CMMT_ITEM_LONG" />
										<xs:element type="xs:string" name="FUNC_AREA_LONG" />
										<xs:element type="xs:string" name="LINE_ID" />
										<xs:element type="xs:string" name="PARENT_ID" />
										<xs:element type="xs:string" name="LINE_DEPTH" />
										<xs:element type="xs:string" name="QUANTITY" />
										<xs:element type="xs:string" name="BASE_UOM" />
										<xs:element type="xs:string" name="LONGNUM" />
										<xs:element type="xs:string" name="BUDGET_PERIOD" />
										<xs:element type="xs:string" name="EARMARKED_NUMBER" />
										<xs:element type="xs:string" name="EARMARKED_ITEM" />
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element maxOccurs="unbounded" name="Serials">
								<xs:complexType>
									<xs:sequence>
										<xs:element type="xs:string" name="SERIALNO" />
										<xs:element type="xs:string" name="UII" />
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="KanbanStatusChange">
					<xs:complexType>
						<xs:sequence>
							<xs:element type="xs:string" name="ORDERID" />
							<xs:element type="xs:string" name="KANBANIDNUMBER" />
							<xs:element type="xs:string" name="NEXTSTATUS" />
						</xs:sequence>
					</xs:complexType>
				</xs:element>				
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>