"use strict";

require("dotenv").config();
const xml2js = require("xml2js");
const builder = new xml2js.Builder();
const parseString = xml2js.parseString;
const SchemaJoi = require("../schema/RfcSchema");
const createError = require("http-errors");
const rfcClient = require("node-rfc").Client;
const fs = require("fs");
var options = {
  explicitArray: false,
  trim: true,
  explicitRoot: false,
};

module.exports = class sapClient {
  #sap;

  constructor(req) {
    this.#sap = new rfcClient(req.session.abapSystem);
    this.contentyype = req.session.ContentType;
    req.session.destroy();
  }

  //Format Error message based on Content-Type
  setError(ex) {
    let respErr = ex;
    if (this.contentyype === "xml") {
      respErr = builder.buildObject(respErr);
    }
    return respErr;
  }

  //Rest url Query Parameters
  sd_query_validation(query) {
    return new Promise(async (resolve, reject) => {
      try {
        await Schema<PERSON>oi.MatDocNo.validateAsync(query);
        resolve({
          MATERIALDOCUMENT: query.MaterialDoc,
          MATDOCUMENTYEAR: query.DocYear,
        });
      } catch (ex) {
        reject(
          this.setError({
            RETURN: [{ TYPE: "E", MESSAGE: ex.details[0].message }],
          })
        );
      }
    });
  }

  //Retrieve Goods Movement Details from SAP & Format Data
  get_GoodsMvt_details(rfcExport) {
    return new Promise(async (resolve, reject) => {
      try {
        await this.#sap.open();
        const rfcImport = await this.#sap.call(
          "BAPI_GOODSMVT_GETDETAIL",
          rfcExport
        );
        await this.#sap.close();
        const jsonObj = {};
        if (rfcImport.GOODSMVT_HEADER.MAT_DOC) {
          jsonObj.HEADER = rfcImport.GOODSMVT_HEADER;
        }
        if (rfcImport.GOODSMVT_ITEMS.length > 0) {
          jsonObj.ITEMS = rfcImport.GOODSMVT_ITEMS;
        }
        if (rfcImport.RETURN.length > 0) {
          jsonObj.RETURN = rfcImport.RETURN;
        }
        if (this.contentyype === "xml") {
          const xmlObj = builder.buildObject(jsonObj);
          resolve(xmlObj);
        } else {
          resolve(jsonObj);
        }
      } catch (ex) {
        reject(this.setError({ RETURN: [{ TYPE: "E", MESSAGE: ex }] }));
      }
    });
  }

  // Retrieve Json/Json Field Text/xml/xsd Definatiuons
  get_payload_descriptions(query) {
    return new Promise(async (resolve, reject) => {
      try {
        let outPut = {};
        if (query["payload"]) {
          switch (String(query["payload"]).toUpperCase()) {
            case "XML":
              outPut.data = fs.readFileSync("payload/gdsMvntPayload.xml");
              outPut.ct = "application/xml";
              break;
            case "XSD":
              outPut.data = fs.readFileSync("payload/gdsMvntPayload.xsd");
              outPut.ct = "application/xml";
              break;
            case "TEXT":
              outPut.data = JSON.parse(
                fs.readFileSync("payload/gdsMvntPayloadDesc.json")
              );
              outPut.ct = "application/json";
              break;
            case "JSON":
              outPut.data = JSON.parse(
                fs.readFileSync("payload/gdsMvntPayload.json")
              );
              outPut.ct = "application/json";
              break;
            default:
              reject(
                this.setError({
                  RETURN: [{ TYPE: "E", MESSAGE: "Not Found!" }],
                })
              );
              break;
          }
        } else {
          outPut.data = JSON.parse(
            fs.readFileSync("payload/gdsMvntPayload.json")
          );
          outPut.ct = "application/json";
        }
        resolve(outPut);
      } catch (ex) {
        reject(this.setError({ RETURN: [{ TYPE: "E", MESSAGE: ex }] }));
      }
    });
  }

  //XML to Json transformation if required
  xml_json_transformation(payload) {
    return new Promise((resolve, reject) => {
      try {
        if (this.contentyype === "xml") {
          parseString(payload, options, (erx, result) => {
            if (erx) {
              reject(this.setError({ RETURN: [{ TYPE: "E", MESSAGE: erx }] }));
            } else {
              if (result["Items"]) {
                if (Array.isArray(result["Items"])) {
                  //Is Array
                  result["Items"].forEach((Item) => {
                    if (Item["Serials"] && !Array.isArray(Item["Serials"])) {
                      Item["Serials"] = [Item["Serials"]];
                    }
                  });
                } else {
                  //Is Not Array
                  if (
                    result.Items["Serials"] &&
                    !Array.isArray(result.Items["Serials"])
                  ) {
                    result.Items["Serials"] = [result.Items["Serials"]];
                  }
                  result["Items"] = [result["Items"]];
                }
              }
              resolve(result);
            }
          });
        } else {
          resolve(payload);
        }
      } catch (ex) {
        let lv_return = {};
        lv_return.RETURN = [{ TYPE: "E", MESSAGE: ex }];
        if (payload.Header.REF_DOC_NO)
          lv_return.REF_DOC_NO = payload.Header.REF_DOC_NO;
        if (payload.Header.REF_DOC_NO_LONG)
          lv_return.REF_DOC_NO_LONG = payload.Header.REF_DOC_NO_LONG;
        reject(this.setError(lv_return));
      }
    });
  }

  //validate the Payload Data whether in write format
  sd_payload_create_validation(payload) {
    return new Promise(async (resolve, reject) => {
      try {
        await SchemaJoi.MatDocCreate.validateAsync(payload);
        resolve(payload);
      } catch (ex) {
        let lv_return = {};
        lv_return.RETURN = [{ TYPE: "E", MESSAGE: ex.details[0].message }];
        if (payload.Header.REF_DOC_NO)
          lv_return.REF_DOC_NO = payload.Header.REF_DOC_NO;
        if (payload.Header.REF_DOC_NO_LONG)
          lv_return.REF_DOC_NO_LONG = payload.Header.REF_DOC_NO_LONG;
        reject(this.setError(lv_return));
      }
    });
  }

  //Prepare data structure for RFC export
  sd_rfc_export(bodyPayload) {
    return new Promise(async (resolve, reject) => {
      try {
        const exporting = { GOODSMVT_CODE: {} };
        if (bodyPayload.Header) {
          exporting.GOODSMVT_CODE.GM_CODE = bodyPayload.Header.GOODSMVT_CODE;
          delete bodyPayload.Header.GOODSMVT_CODE;
          exporting.GOODSMVT_HEADER = bodyPayload.Header;
        }
        if (bodyPayload.Items && Array.isArray(bodyPayload.Items)) {
          exporting.GOODSMVT_ITEM = [];
          let MatDocItem = 0;
          bodyPayload.Items.forEach((e) => {
            MatDocItem += 1;
            if (!isNaN(e.Item.MATERIAL)) {
              e.Item.MATERIAL = ("000000000000000000" + e.Item.MATERIAL).slice(
                -18
              );
            }
            if (!isNaN(e.Item.COSTCENTER)) {
              e.Item.COSTCENTER = ("0000000000" + e.Item.COSTCENTER).slice(-10);
            }
            exporting.GOODSMVT_ITEM.push(e.Item);
            if (e.Serials && Array.isArray(e.Serials)) {
              if (!exporting.GOODSMVT_SERIALNUMBER)
                exporting.GOODSMVT_SERIALNUMBER = [];
              e.Serials.forEach((s) => {
                s.MATDOC_ITM = ("0000" + String(MatDocItem)).slice(-4);
                exporting.GOODSMVT_SERIALNUMBER.push(s);
              });
            }
          });
        }
        // Kanban Status Change
        if (bodyPayload.KanbanStatusChange) {
          if (bodyPayload.KanbanStatusChange.ORDERID) {
            bodyPayload.KanbanStatusChange.KANBANIDNUMBER =
              await this.production_Order_KanbanId(
                bodyPayload.KanbanStatusChange.ORDERID
              );
          }
          exporting.KanbanStatusChange = bodyPayload.KanbanStatusChange;
        }
        resolve(exporting);
      } catch (ex) {
        let lv_return = {};
        lv_return.RETURN = [{ TYPE: "E", MESSAGE: ex }];
        if (bodyPayload.Header.REF_DOC_NO)
          lv_return.REF_DOC_NO = bodyPayload.Header.REF_DOC_NO;
        if (bodyPayload.Header.REF_DOC_NO_LONG)
          lv_return.REF_DOC_NO_LONG = bodyPayload.Header.REF_DOC_NO_LONG;
        reject(this.setError(lv_return));
      }
    });
  }

  production_Order_KanbanId(OrderId) {
    return new Promise(async (resolve, reject) => {
      try {
        await this.#sap.open();
        const import_pkps = await this.#sap.call("RFC_READ_TABLE", {
          QUERY_TABLE: "PKPS",
          DELIMITER: "|",
          OPTIONS: [`AUFNR = '${OrderId}' `],
          FIELDS: [
            {
              FIELDNAME: "MANDT",
              OFFSET: "000000",
              LENGTH: "000003",
              TYPE: "C",
            },
            {
              FIELDNAME: "PKKEY",
              OFFSET: "000004",
              LENGTH: "000010",
              TYPE: "N",
            },
            {
              FIELDNAME: "AUFNR",
              OFFSET: "000015",
              LENGTH: "000012",
              TYPE: "C",
            },
          ],
        });
        await this.#sap.close();

        if (import_pkps.DATA.length > 0) {
          const WA = import_pkps.DATA[0].WA.split("|");
          resolve(WA[1]);
        } else {
          reject(
            `Production Order ${OrderId} not found for kanban status change.`
          );
        }
      } catch (err) {
        reject(err);
      }
    });
  }

  // Goode Movement Create using rfc
  sd_GoodsMovement_Create(rfcExport) {
    return new Promise(async (resolve, reject) => {
      try {
        //Check Kanban Status Change if any
        const KanbanStatusChange = rfcExport.KanbanStatusChange;
        delete rfcExport.KanbanStatusChange;

        // resolve({ rfcExport });
        await this.#sap.open();
        const rfcImport = await this.#sap.call(
          "BAPI_GOODSMVT_CREATE",
          rfcExport
        );

        if (rfcImport.RETURN && rfcImport.RETURN.find((r) => r.TYPE === "E")) {
          await this.#sap.close();
          let lv_return = { RETURN: rfcImport.RETURN };
          if (rfcExport.GOODSMVT_HEADER.REF_DOC_NO)
            lv_return.REF_DOC_NO = rfcExport.GOODSMVT_HEADER.REF_DOC_NO;
          if (rfcExport.GOODSMVT_HEADER.REF_DOC_NO_LONG)
            lv_return.REF_DOC_NO_LONG =
              rfcExport.GOODSMVT_HEADER.REF_DOC_NO_LONG;
          reject(this.setError(lv_return));

        } else {
          let respSucc = rfcImport.GOODSMVT_HEADRET;

          //If there is any KanBan Status change is Required
          if (KanbanStatusChange) {
            const rfcImportKanban = await this.#sap.call(
              "BAPI_KANBAN_CHANGESTATUS1",
              {
                KANBANIDNUMBER: KanbanStatusChange.KANBANIDNUMBER,
                NEXTSTATUS: KanbanStatusChange.NEXTSTATUS,
              }
            );
            if (rfcImportKanban.RETURN.TYPE === "E") {
              respSucc.KANBAN_RETURN = rfcImportKanban.RETURN;
              delete respSucc.MAT_DOC;
              delete respSucc.DOC_YEAR;
            } else {
              respSucc.KANBAN_STATUSCHANGERESULT =
                rfcImportKanban.STATUSCHANGERESULT;
            }
          }

          if (!respSucc?.KANBAN_RETURN) await this.#sap.call("BAPI_TRANSACTION_COMMIT", {})

          await this.#sap.close();

          if (rfcExport.GOODSMVT_HEADER.REF_DOC_NO) respSucc.REF_DOC_NO = rfcExport.GOODSMVT_HEADER.REF_DOC_NO;

          if (rfcExport.GOODSMVT_HEADER.REF_DOC_NO_LONG) respSucc.REF_DOC_NO_LONG = rfcExport.GOODSMVT_HEADER.REF_DOC_NO_LONG;

          if (this.contentyype === "xml") respSucc = builder.buildObject(respSucc)

          resolve(respSucc);
        }
      } catch (ex) {
        await this.#sap.close();
        let lv_return = {};
        lv_return.RETURN = [{ TYPE: "E", MESSAGE: ex }];
        if (rfcExport.GOODSMVT_HEADER.REF_DOC_NO)
          lv_return.REF_DOC_NO = rfcExport.GOODSMVT_HEADER.REF_DOC_NO;
        if (rfcExport.GOODSMVT_HEADER.REF_DOC_NO_LONG)
          lv_return.REF_DOC_NO_LONG = rfcExport.GOODSMVT_HEADER.REF_DOC_NO_LONG;
        reject(this.setError(lv_return));
      }
    });
  }

  MatDocUpdate_payload_validation(reqBody) {
    return new Promise((resolve, reject) => {
      const result = SchemaJoi.MatDocUpdate.validate(reqBody);
      if (result?.error) {
        reject(createError(400, result?.error?.details[0]?.message));
      } else {
        resolve(result?.value);
      }
    })
  }

  set_shdb_screen_control(req) {
    return new Promise(async (resolve, reject) => {
      await this.#sap.open();
      try {
        const { DATA: dataT063 } = await this.#sap.call("RFC_READ_TABLE", {
          QUERY_TABLE: "T063",
          DELIMITER: "|",
          OPTIONS: [{ TEXT: `TRTYP = 'V'` }]
        });
        await this.#sap.close();

        const tabT063 = [];
        dataT063.forEach(t063 => {
          tabT063.push({
            VGART: t063.WA.slice(0, 2),
            FCODE: t063.WA.slice(3, 20).trim(),
            TRTYP: t063.WA.slice(24, 25),
            KZBEW: t063.WA.slice(26, 27),
            DYNNR: t063.WA.slice(28, 32)
          })
        });

        // Create Screen Control: Inventory Management
        fs.writeFile("./Files/T063Set.json", JSON.stringify(tabT063), (err) => {
          if (err) reject(createError(400, err?.message));
          resolve(tabT063);
        });

      } catch (error) {
        reject(createError(400, error?.message));
      }
    })
  }

  MatDocUpdate_shdb(matDoc) {
    return new Promise(async (resolve, reject) => {

      //Check Number Of Material Document Items
      try {
        await this.#sap.open();
        const mat_doc = await this.#sap.call("BAPI_GOODSMVT_GETDETAIL", {
          MATERIALDOCUMENT: matDoc.Header.MaterialDocumentNumber,
          MATDOCUMENTYEAR: matDoc.Header.MaterialDocumentYear
        });
        await this.#sap.close();

        if (mat_doc.RETURN.length === 0) {
          const bdcdata = [];
          const t063Set = JSON.parse(fs.readFileSync('./Files/T063Set.json'));

          //TCODE ------------------------------------------
          bdcdata.push({ "PROGRAM": "SAPMM07M", "DYNPRO": "0460", "DYNBEGIN": "X" });
          bdcdata.push({ "FNAM": "BDC_CURSOR", "FVAL": "RM07M-MBLNR" });
          bdcdata.push({ "FNAM": "BDC_OKCODE", "FVAL": "AB" });
          bdcdata.push({ "FNAM": "RM07M-MBLNR", "FVAL": matDoc.Header.MaterialDocumentNumber });
          bdcdata.push({ "FNAM": "RM07M-MJAHR", "FVAL": matDoc.Header.MaterialDocumentYear });
          bdcdata.push({ "FNAM": "XFULL", "FVAL": "X" });
          //Overview Screen ------------------------------------                 
          const { DYNNR: dynproAB } = t063Set.find(to63 => (
            to63.VGART === mat_doc.GOODSMVT_HEADER.TR_EV_TYPE &&
            to63.FCODE === "AB" &&
            to63.TRTYP === "V" &&
            to63.KZBEW === " "
          ));
          if (matDoc.Header.HeaderText || matDoc.Header.HeaderText === "") {
            bdcdata.push({ "PROGRAM": "SAPMM07M", "DYNPRO": `${dynproAB}`, "DYNBEGIN": "X" });
            bdcdata.push({ "FNAM": "BDC_CURSOR", "FVAL": "MKPF-BUDAT" });
            bdcdata.push({ "FNAM": "BDC_OKCODE", "FVAL": "=KK" });

            //Header Text ------------------------------------------     
            const { DYNNR: dynproKK } = t063Set.find(to63 => (
              to63.VGART === mat_doc.GOODSMVT_HEADER.TR_EV_TYPE &&
              to63.FCODE === "KK" &&
              to63.TRTYP === "V" &&
              to63.KZBEW === " "
            ));
            bdcdata.push({ "PROGRAM": "SAPMM07M", "DYNPRO": `${dynproKK}`, "DYNBEGIN": "X" });
            bdcdata.push({ "FNAM": "BDC_CURSOR", "FVAL": "MKPF-BKTXT" });
            bdcdata.push({ "FNAM": "BDC_OKCODE", "FVAL": "=AB" });
            bdcdata.push({ "FNAM": "MKPF-BKTXT", "FVAL": matDoc.Header.HeaderText });
          }

          // Item ------------------------------------------
          matDoc?.Items?.forEach(item => {
            const mseg_item = mat_doc.GOODSMVT_ITEMS.find(mseg => mseg.MATDOC_ITM === item.ItemNumber);
            if (mseg_item) {
              // Goto Item Screen ------------------------------------
              bdcdata.push({ "PROGRAM": "SAPMM07M", "DYNPRO": `${dynproAB}`, "DYNBEGIN": "X" });
              bdcdata.push({ "FNAM": "BDC_CURSOR", "FVAL": "MKPF-BUDAT" });
              bdcdata.push({ "FNAM": "BDC_OKCODE", "FVAL": "=KPA" });

              if (mat_doc.GOODSMVT_ITEMS.length > 1) {
                bdcdata.push({ "PROGRAM": "SAPMM07M", "DYNPRO": "1501", "DYNBEGIN": "X" });
                bdcdata.push({ "FNAM": "BDC_CURSOR", "FVAL": "RM07M-ZEILE" });
                bdcdata.push({ "FNAM": "BDC_OKCODE", "FVAL": "=OK" });
                bdcdata.push({ "FNAM": "RM07M-ZEILE", "FVAL": item.ItemNumber });
              }

              // Item text ------------------------------------------
              const { DYNNR: dynproKP } = t063Set.find(to63 => (
                to63.VGART === mat_doc.GOODSMVT_HEADER.TR_EV_TYPE &&
                to63.FCODE === "KP" &&
                to63.TRTYP === "V" &&
                to63.KZBEW === mseg_item.MVT_IND
              ));
              bdcdata.push({ "PROGRAM": "SAPMM07M", "DYNPRO": `${dynproKP}`, "DYNBEGIN": "X" });
              bdcdata.push({ "FNAM": "BDC_CURSOR", "FVAL": "MSEG-SGTXT" });
              bdcdata.push({ "FNAM": "BDC_OKCODE", "FVAL": "=AB" });
              if (item.UnloadingPoint || item.UnloadingPoint === "") {
                bdcdata.push({ "FNAM": "MSEG-ABLAD", "FVAL": item.UnloadingPoint })
              }
              if (item.ItemText || item.ItemText === "") {
                bdcdata.push({ "FNAM": "MSEG-SGTXT", "FVAL": item.ItemText })
              }
              bdcdata.push({ "FNAM": "BDC_SUBSCR", "FVAL": "SAPMM07M                                2201BLOCK" });

              if (mat_doc.GOODSMVT_HEADER.EXPIMP_NO === "") {
                // console.log("BLANK: Header.EXPIMP_NO", mat_doc.GOODSMVT_HEADER.EXPIMP_NO);
                // Do Nothing
              } else { //Number of foreign trade data in MM and SD documents
                console.log("NOT_BLANK: Header.EXPIMP_NO", mat_doc.GOODSMVT_HEADER.EXPIMP_NO);
                bdcdata.push({ "PROGRAM": "SAPLV50E", "DYNPRO": "0590", "DYNBEGIN": "X" });
                bdcdata.push({ "FNAM": "BDC_CURSOR", "FVAL": "EIPO-SEGAL" });
                bdcdata.push({ "FNAM": "BDC_OKCODE", "FVAL": "/EV50E_ABBA" });
              }
            } else {
              reject(createError(400, `Item ${item.ItemNumber} not included in this document`));
            }
          })

          // Save Doc --------------------------------------------
          bdcdata.push({ "PROGRAM": "SAPMM07M", "DYNPRO": `${dynproAB}`, "DYNBEGIN": "X" });
          bdcdata.push({ "FNAM": "BDC_CURSOR", "FVAL": "MKPF-BUDAT" });
          bdcdata.push({ "FNAM": "BDC_OKCODE", "FVAL": "=BU" });

          const extnumber = mat_doc.GOODSMVT_HEADER.MAT_DOC + mat_doc.GOODSMVT_HEADER.DOC_YEAR;
          resolve({ bdcdata, extnumber });
        } else {
          reject({ error: mat_doc.RETURN.map(ret => ({ MESSAGE: ret.MESSAGE })) });
        }
      } catch (error) {
        reject(createError(400, error?.message));
      }
    })
  }

  MatDocUpdate_rfc({ bdcdata, extnumber }) {
    return new Promise(async (resolve, reject) => {
      try {
        await this.#sap.open();
        const respRfcDoc = await this.#sap.call(
          "ZSD_DELIVERY_UPDATE_ASYNC",
          {
            TCODE: "MB02",
            OPTIONS: {
              DISMODE: "N",
              UPDMODE: "A",
              DEFSIZE: "X"
            },
            BDCDATA: bdcdata,
            EXTNUMBER: extnumber
          }
        );
        const { BDCMSGCOLL } = respRfcDoc;
        const RETURN = [];
        for (const msg of BDCMSGCOLL) {
          const { MSGTYP, MSGID, MSGNR, MSGV1, MSGV2, MSGV3, MSGV4 } = msg;
          const TEXTFORMAT = "NON"; // No long text should be output.
          const LANGUAGE = "E";     // Language Always English
          const { MESSAGE } = await this.#sap.call("BAPI_MESSAGE_GETDETAIL", {
            ID: MSGID,
            NUMBER: MSGNR,
            LANGUAGE,
            MESSAGE_V1: MSGV1,
            MESSAGE_V2: MSGV2,
            MESSAGE_V3: MSGV3,
            MESSAGE_V4: MSGV4,
            TEXTFORMAT
          })
          RETURN.push({ MSGTYP, MESSAGE })
        }
        await this.#sap.close();

        resolve({ RETURN })
      } catch (error) {
        reject(createError(400, error?.message));
      }
    })
  }

};
