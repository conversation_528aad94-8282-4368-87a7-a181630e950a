try {
    Write-Host "Starting conversion process..."
    
    # Create Word Application
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    
    # File paths
    $mdPath = Join-Path $PSScriptRoot "GoodsMovementCreate_Field_Mapping_Documentation.md"
    $docxPath = Join-Path $PSScriptRoot "GoodsMovementCreate_Field_Mapping_Documentation.docx"
    
    Write-Host "Opening file: $mdPath"
    
    # Open the markdown file
    $doc = $word.Documents.Open($mdPath)
    
    Write-Host "Saving as DOCX: $docxPath"
    
    # Save as DOCX (format 16 = wdFormatXMLDocument)
    $doc.SaveAs2($docxPath, 16)
    
    # Close document and quit Word
    $doc.Close()
    $word.Quit()
    
    # Release COM objects
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($doc) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($word) | Out-Null
    
    Write-Host "SUCCESS: Document converted to DOCX successfully!"
    Write-Host "File saved at: $docxPath"
    
} catch {
    Write-Host "ERROR: Could not convert document"
    Write-Host "Error details: $($_.Exception.Message)"
    Write-Host ""
    Write-Host "This might happen if:"
    Write-Host "1. Microsoft Word is not installed"
    Write-Host "2. Word is currently running and locked"
    Write-Host "3. Insufficient permissions"
    Write-Host ""
    Write-Host "Alternative solutions:"
    Write-Host "1. Open the .md file directly in Microsoft Word"
    Write-Host "2. Copy content and paste into Word manually"
    Write-Host "3. Use online converter: https://pandoc.org/try/"
}
