{"Header": {"GOODSMVT_CODE": "Assign Code to Transaction for Goods Movement", "PSTNG_DATE": "Posting Date in the Document", "DOC_DATE": "Document Date in Document", "REF_DOC_NO": "Reference Document Number", "BILL_OF_LADING": "Number of bill of lading at time of goods receipt", "GR_GI_SLIP_NO": "Goods Receipt/Issue Slip Number", "PR_UNAME": "User Name", "HEADER_TXT": "Document Header Text", "VER_GR_GI_SLIP": "Version for Printing GR/GI Slip", "VER_GR_GI_SLIPX": "Updated information in related user data field", "EXT_WMS": "Control posting for external WMS", "REF_DOC_NO_LONG": "Reference Document Number (for Dependencies see Long Text)", "BILL_OF_LADING_LONG": "Bill of Lading Number in GR (Dependencies: see Long Text)", "BAR_CODE": "Bar code entry"}, "Items": [{"Item": {"MATERIAL": "Material Number", "PLANT": "Plant", "STGE_LOC": "Storage Location", "BATCH": "Batch Number", "MOVE_TYPE": "Movement Type (Inventory Management)", "STCK_TYPE": "Stock Type", "SPEC_STOCK": "Special Stock Indicator", "VENDOR": "Vendor Account Number", "CUSTOMER": "Account Number of Customer", "SALES_ORD": "Sales Order Number", "S_ORD_ITEM": "Item Number in Sales Order", "SCHED_LINE": "Delivery Schedule for Sales Order", "VAL_TYPE": "Valuation Type", "ENTRY_QNT": "Quantity in Unit of Entry", "ENTRY_UOM": "Unit of Entry", "ENTRY_UOM_ISO": "ISO code for unit of measurement", "PO_PR_QNT": "Quantity in Purchase Order Price Unit", "ORDERPR_UN": "Order Price Unit (Purchasing)", "ORDERPR_UN_ISO": "ISO code for unit of measurement", "PO_NUMBER": "Purchase Order Number", "PO_ITEM": "Item Number of Purchasing Document", "SHIPPING": "Shipping Instructions", "COMP_SHIP": "Compliance with Shipping Instructions", "NO_MORE_GR": "\"Delivery Completed\" Indicator", "ITEM_TEXT": "Item Text", "GR_RCPT": "Goods Recipient/Ship-To Party", "UNLOAD_PT": "Unloading Point", "COSTCENTER": "Cost Center", "ORDERID": "Order Number", "ORDER_ITNO": "Order Item Number", "CALC_MOTIVE": "Accounting Indicator", "ASSET_NO": "Main Asset Number", "SUB_NUMBER": "Asset Subnumber", "RESERV_NO": "Number of Reservation/Dependent Requirement", "RES_ITEM": "Item Number of Reservation/Dependent Requirement", "RES_TYPE": "Record type", "WITHDRAWN": "Final Issue for This Reservation", "MOVE_MAT": "Receiving/Issuing Material", "MOVE_PLANT": "Receiving/Issuing Plant", "MOVE_STLOC": "Receiving/Issuing Storage Location", "MOVE_BATCH": "Receiving/Issuing Batch", "MOVE_VAL_TYPE": "Valuation Type of Transfer Batch", "MVT_IND": "Movement Indicator", "MOVE_REAS": "Reason for Movement", "RL_EST_KEY": "Internal Key for Real Estate Object", "REF_DATE": "Reference Date for Settlement", "COST_OBJ": "Cost Object", "PROFIT_SEGM_NO": "Profitability Segment Number (CO-PA)", "PROFIT_CTR": "Profit Center", "WBS_ELEM": "Work Breakdown Structure Element (WBS Element)", "NETWORK": "Network Number for Account Assignment", "ACTIVITY": "Operation/Activity Number", "PART_ACCT": "Partner Account Number", "AMOUNT_LC": "Externally entered posting amount in local currency", "AMOUNT_SV": "Externally entered sales value in local currency", "REF_DOC_YR": "Fiscal Year of a Reference Document", "REF_DOC": "Document No. of a Reference Document", "REF_DOC_IT": "Item of a Reference Document", "EXPIRYDATE": "Shelf Life Expiration or Best-Before Date", "PROD_DATE": "Date of Manufacture", "FUND": "Fund", "FUNDS_CTR": "Funds Center", "CMMT_ITEM": "Commitment Item", "VAL_SALES_ORD": "Sales Order Number of Valuated Sales Order Stock", "VAL_S_ORD_ITEM": "Sales Order Item of Valuated Sales Order Stock", "VAL_WBS_ELEM": "Work Breakdown Structure Element (WBS Element)", "GL_ACCOUNT": "G/L Account Number", "IND_PROPOSE_QUANX": "Propose quantities", "XSTOB": "Use Reversal Movement Type Indicator", "EAN_UPC": "International Article Number (EAN/UPC)", "DELIV_NUMB_TO_SEARCH": "Delivery", "DELIV_ITEM_TO_SEARCH": "Delivery Item", "SERIALNO_AUTO_NUMBERASSIGNMENT": "Create serial number automatically", "VENDRBATCH": "<PERSON><PERSON><PERSON>", "STGE_TYPE": "Storage Type", "STGE_BIN": "Storage Bin", "SU_PL_STCK_1": "Number of Storage Units to be Placed Into Storage", "ST_UN_QTYY_1": "Quantity per Storage Unit to be Placed into Stock in Alt.UoM", "ST_UN_QTYY_1_ISO": "ISO code for unit of measurement", "UNITTYPE_1": "Storage Unit Type", "SU_PL_STCK_2": "Number of Storage Units to be Placed Into Storage", "ST_UN_QTYY_2": "Quantity per Storage Unit to be Placed into Stock in Alt.UoM", "ST_UN_QTYY_2_ISO": "ISO code for unit of measurement", "UNITTYPE_2": "Storage Unit Type", "STGE_TYPE_PC": "Storage type for transfer posting", "STGE_BIN_PC": "Storage bin for transfer posting", "NO_PST_CHGNT": "Indicator: Do not create posting change notice", "GR_NUMBER": "Goods Receipt Number", "STGE_TYPE_ST": "Storage type for stock transfer", "STGE_BIN_ST": "Storage bin for stock transfer", "MATDOC_TR_CANCEL": "Material doc. no. of transfer requirement to be cancelled", "MATITEM_TR_CANCEL": "Material doc. item of transf.reqmnt item to be cancelled", "MATYEAR_TR_CANCEL": "Material doc. year of transfer requirement to be cancelled", "NO_TRANSFER_REQ": "Indicator: No Transfer Requirement Created", "CO_BUSPROC": "Business Process", "ACTTYPE": "Activity Type", "SUPPL_VEND": "Supplying Vendor", "MATERIAL_EXTERNAL": "Long Material Number for MATERIAL Field", "MATERIAL_GUID": "External GUID for MATERIAL Field", "MATERIAL_VERSION": "Version Number for MATERIAL Field", "MOVE_MAT_EXTERNAL": "Long Material Number for MOVE_MAT Field", "MOVE_MAT_GUID": "External GUID for MOVE_MAT Field", "MOVE_MAT_VERSION": "Version Number for MOVE_MAT Field", "FUNC_AREA": "Functional Area", "TR_PART_BA": "Trading Partner's Business Area", "PAR_COMPCO": "Clearing company code", "DELIV_NUMB": "Delivery", "DELIV_ITEM": "Delivery Item", "NB_SLIPS": "Number of GR/GI Slips to Be Printed", "NB_SLIPSX": "Updated information in related user data field", "GR_RCPTX": "Updated information in related user data field", "UNLOAD_PTX": "Updated information in related user data field", "SPEC_MVMT": "Special movement indicator for warehouse management", "GRANT_NBR": "<PERSON>", "CMMT_ITEM_LONG": "Commitment Item", "FUNC_AREA_LONG": "Functional Area", "LINE_ID": "Unique identification of document line", "PARENT_ID": "Identifier of immediately superior line", "LINE_DEPTH": "Hierarchy level of line in document", "QUANTITY": "Quantity", "BASE_UOM": "Base Unit of Measure", "LONGNUM": "Smart Number", "BUDGET_PERIOD": "FM: Budget Period", "EARMARKED_NUMBER": "Document Number for Earmarked Funds", "EARMARKED_ITEM": "Earmarked Funds: Document Item"}, "Serials": [{"SERIALNO": "Serial Number", "UII": "Unique Item Identifier"}]}], "KanbanStatusChange": {"ORDERID": "Production Order Number", "KANBANIDNUMBER": "Identification Number", "NEXTSTATUS": "Kanban status"}}