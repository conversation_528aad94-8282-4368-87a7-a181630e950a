"use strict";

require("dotenv").config();
const authentication = require("../modules/authentication");
const sapClient = require("../modules/sapClient");
const express = require("express");
const router = express.Router();

// GET : Retrieve Material Document Details
router.get("/MaterialDocument", authentication, (req, res) => {
  const sap = new sapClient(req);
  sap
    .sd_query_validation(req.query)
    .then((rfcExport) => sap.get_GoodsMvt_details(rfcExport))
    .then((MatDocu) => {
      res.setHeader("Content-Type", req.get("Content-Type"));
      res.status(200).send(MatDocu);
    })
    .catch((err) => {
      res.setHeader("Content-Type", req.get("Content-Type"));
      res.status(400).send(err);
    });
});

// POST : Update Material Document Details
router.post("/MaterialDocumentChange", authentication, (req, res) => {
  const sap = new sapClient(req);
  sap
    .MatDocUpdate_payload_validation(req.body)
    .then((delvPayload) => sap.MatDocUpdate_shdb(delvPayload))
    .then((bdcdata) => sap.MatDocUpdate_rfc(bdcdata))
    .then((MatDocu) => res.status(200).send(MatDocu))
    .catch((err) => res.status(400).send(err));
});

// GET : Goods Movement - Payload
router.get("/GoodsMovementCreate", authentication, (req, res) => {
  const sap = new sapClient(req);
  sap
    .get_payload_descriptions(req.query)
    .then((mDoc) => {
      res.setHeader("Content-Type", mDoc.ct);
      res.status(200).send(mDoc.data);
    })
    .catch((err) => {
      res.setHeader("Content-Type", req.get("Content-Type"));
      res.status(400).send(err);
    });
});

// POST : Goods Movement
router.post("/GoodsMovementCreate", authentication, (req, res) => {
  const sap = new sapClient(req);
  sap
    .xml_json_transformation(req.body)
    .then((payload) => sap.sd_payload_create_validation(payload))
    .then((MatDocu) => sap.sd_rfc_export(MatDocu))
    .then((rfcExport) => sap.sd_GoodsMovement_Create(rfcExport))
    .then((rfcImport) => {
      res.setHeader("Content-Type", req.get("Content-Type"));
      res.status(200).send(rfcImport);
    })
    .catch((err) => {
      res.setHeader("Content-Type", req.get("Content-Type"));
      res.status(400).send(err);
    });
});

// POST : Screen Control: Inventory Management
router.get("/T063Set", authentication, (req, res) => {
  const sap = new sapClient(req);
  sap
    .set_shdb_screen_control(req)
    .then((rfcImport) => res.status(200).send(rfcImport))
    .catch((err) => res.status(400).send(err));
});

module.exports = router;
