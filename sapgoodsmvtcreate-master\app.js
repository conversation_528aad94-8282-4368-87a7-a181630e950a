"use strict";

require("dotenv").config();
const express = require("express");
const session = require("express-session");
const cookieParser = require("cookie-parser");
const logger = require("morgan");
const helmet = require("helmet");
const router = require("./routes/router");
const app = express();

app.set("port", process.env.PORT || 3000);
app.use(helmet());
app.use(logger("dev"));
app.use(
  session({
    resave: true,
    saveUninitialized: true,
    secret: process.env.APISECRET,
  })
);
app.use(express.json({ type: "application/json" }));
app.use(express.json({ type: "text/json" }));
app.use(express.text({ type: "application/xml" }));
app.use(express.text({ type: "text/xml" }));
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.disable("x-powered-by");

// Validate Content-Type
app.use((req, res, next) => {
  let contype = req.headers["content-type"];
  console.log(contype);
  if (contype === "application/json") {
    next();
  } else if (contype === "application/xml") {
    next();
  } else {
    res.setHeader("Content-Type", "text/plain");
    res.status(415).send("Supports only Xml/Json");
  }
});

//Expected Route
app.use("/sap", router);

//Invalid Path
app.use((req, res) => {
  res.setHeader("Content-Type", "text/plain");
  res.status(400).send("Invalid Path!");
});

//An error handling middleware
app.use((error, req, res, next) => {
  try {
    const exRes = JSON.parse(error.message);
    res.status(exRes.status || 500).send({ error: exRes.body });
  } catch (ex) {
    console.log(error.message);
    res.status(500).send({ error: error.message });
  }
});

// Instantiate the server
app.listen(app.get("port"), () =>
  console.log(`Server listening on port ${app.get("port")}`)
);
