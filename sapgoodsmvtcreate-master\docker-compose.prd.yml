version: "3.3"
services:
  sapgoodsmvtcreate:
    build: .
    image: sap/sapgoodsmvtcreate
    container_name: sapgoodsmvtcreate
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 3G
        reservations:
          cpus: '0.25'
          memory: 2G
    ports:
      - "5790:5790"
    env_file:
      - "./config/config.env"
    environment:
      - PORT=5790
      - SAPSYSID=NP1
      - ASHOST=ud3-sv-sapec3.matw.matthewsintl.com
      - SYSNR=00
      - CLIENT=300
      - LANGU=EN
      - RFC_TRACE=0
      - JWTLINK=http://c3mft01lp.amer.schawk.com:8081/auth/.well-known/jwks.json
      - JWTLOGIN=http://c3mft01lp.amer.schawk.com:8081/auth/login
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
