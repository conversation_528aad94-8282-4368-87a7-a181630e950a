"use strict";

const Joi = require("joi").extend(require("@joi/date"));
const SchemaJ<PERSON> = new Object();

//Sales order Number
SchemaJoi.MatDocNo = Joi.object().keys({
  MaterialDoc: Joi.string().max(10).required(),
  DocYear: Joi.string().min(4).max(4).required(),
});

SchemaJoi.MatDocCreate = Joi.object({
  Header: Joi.object({
    GOODSMVT_CODE: Joi.string().max(2).required(),
    PSTNG_DATE: Joi.date().format("YYYYMMDD"),
    DOC_DATE: Joi.date().format("YYYYMMDD"),
    REF_DOC_NO: Joi.string().max(32),
    BILL_OF_LADING: Joi.string().max(32),
    GR_GI_SLIP_NO: Joi.string().max(20),
    PR_UNAME: Joi.string().max(24),
    HEADER_TXT: Joi.string().max(50),
    VER_GR_GI_SLIP: Joi.string().max(2),
    VER_GR_GI_SLIPX: Joi.string().max(2),
    EXT_WMS: Joi.string().max(2),
    REF_DOC_NO_LONG: Joi.string().max(70),
    BILL_OF_LADING_LONG: Joi.string().max(70),
    BAR_CODE: Joi.string().max(80),
  }),
  Items: Joi.array().items({
    Item: Joi.object({
      MATERIAL: Joi.string().max(36),
      PLANT: Joi.string().max(8),
      STGE_LOC: Joi.string().max(8),
      BATCH: Joi.string().max(20),
      MOVE_TYPE: Joi.string().max(6),
      STCK_TYPE: Joi.string().max(2),
      SPEC_STOCK: Joi.string().max(2),
      VENDOR: Joi.string().max(20),
      CUSTOMER: Joi.string().max(20),
      SALES_ORD: Joi.string().max(20),
      S_ORD_ITEM: Joi.string().max(12),
      SCHED_LINE: Joi.string().max(8),
      VAL_TYPE: Joi.string().max(20),
      ENTRY_QNT: Joi.string().max(7),
      ENTRY_UOM: Joi.string().max(6),
      ENTRY_UOM_ISO: Joi.string().max(6),
      PO_PR_QNT: Joi.string().max(7),
      ORDERPR_UN: Joi.string().max(6),
      ORDERPR_UN_ISO: Joi.string().max(6),
      PO_NUMBER: Joi.string().max(20),
      PO_ITEM: Joi.string().max(10),
      SHIPPING: Joi.string().max(4),
      COMP_SHIP: Joi.string().max(4),
      NO_MORE_GR: Joi.string().max(2),
      ITEM_TEXT: Joi.string().max(100),
      GR_RCPT: Joi.string().max(24),
      UNLOAD_PT: Joi.string().max(50),
      COSTCENTER: Joi.string().max(20),
      ORDERID: Joi.string().max(24),
      ORDER_ITNO: Joi.string().max(8),
      CALC_MOTIVE: Joi.string().max(4),
      ASSET_NO: Joi.string().max(24),
      SUB_NUMBER: Joi.string().max(8),
      RESERV_NO: Joi.string().max(20),
      RES_ITEM: Joi.string().max(8),
      RES_TYPE: Joi.string().max(2),
      WITHDRAWN: Joi.string().max(2),
      MOVE_MAT: Joi.string().max(36),
      MOVE_PLANT: Joi.string().max(8),
      MOVE_STLOC: Joi.string().max(8),
      MOVE_BATCH: Joi.string().max(20),
      MOVE_VAL_TYPE: Joi.string().max(20),
      MVT_IND: Joi.string().max(1),
      MOVE_REAS: Joi.string().max(8),
      RL_EST_KEY: Joi.string().max(16),
      REF_DATE: Joi.date().format("YYYYMMDD"),
      COST_OBJ: Joi.string().max(24),
      PROFIT_SEGM_NO: Joi.string().max(20),
      PROFIT_CTR: Joi.string().max(20),
      WBS_ELEM: Joi.string().max(48),
      NETWORK: Joi.string().max(24),
      ACTIVITY: Joi.string().max(8),
      PART_ACCT: Joi.string().max(20),
      AMOUNT_LC: Joi.string().max(12),
      AMOUNT_SV: Joi.string().max(12),
      REF_DOC_YR: Joi.string().max(8),
      REF_DOC: Joi.string().max(20),
      REF_DOC_IT: Joi.string().max(8),
      EXPIRYDATE: Joi.date().format("YYYYMMDD"),
      PROD_DATE: Joi.date().format("YYYYMMDD"),
      FUND: Joi.string().max(20),
      FUNDS_CTR: Joi.string().max(32),
      CMMT_ITEM: Joi.string().max(28),
      VAL_SALES_ORD: Joi.string().max(20),
      VAL_S_ORD_ITEM: Joi.string().max(12),
      VAL_WBS_ELEM: Joi.string().max(48),
      GL_ACCOUNT: Joi.string().max(20),
      IND_PROPOSE_QUANX: Joi.string().max(2),
      XSTOB: Joi.string().max(2),
      EAN_UPC: Joi.string().max(36),
      DELIV_NUMB_TO_SEARCH: Joi.string().max(20),
      DELIV_ITEM_TO_SEARCH: Joi.string().max(12),
      SERIALNO_AUTO_NUMBERASSIGNMENT: Joi.string().max(2),
      VENDRBATCH: Joi.string().max(30),
      STGE_TYPE: Joi.string().max(6),
      STGE_BIN: Joi.string().max(20),
      SU_PL_STCK_1: Joi.string().max(2),
      ST_UN_QTYY_1: Joi.string().max(7),
      ST_UN_QTYY_1_ISO: Joi.string().max(6),
      UNITTYPE_1: Joi.string().max(6),
      SU_PL_STCK_2: Joi.string().max(2),
      ST_UN_QTYY_2: Joi.string().max(7),
      ST_UN_QTYY_2_ISO: Joi.string().max(6),
      UNITTYPE_2: Joi.string().max(6),
      STGE_TYPE_PC: Joi.string().max(6),
      STGE_BIN_PC: Joi.string().max(20),
      NO_PST_CHGNT: Joi.string().max(2),
      GR_NUMBER: Joi.string().max(20),
      STGE_TYPE_ST: Joi.string().max(6),
      STGE_BIN_ST: Joi.string().max(20),
      MATDOC_TR_CANCEL: Joi.string().max(20),
      MATITEM_TR_CANCEL: Joi.string().max(8),
      MATYEAR_TR_CANCEL: Joi.string().max(8),
      NO_TRANSFER_REQ: Joi.string().max(2),
      CO_BUSPROC: Joi.string().max(24),
      ACTTYPE: Joi.string().max(12),
      SUPPL_VEND: Joi.string().max(20),
      MATERIAL_EXTERNAL: Joi.string().max(80),
      MATERIAL_GUID: Joi.string().max(64),
      MATERIAL_VERSION: Joi.string().max(20),
      MOVE_MAT_EXTERNAL: Joi.string().max(80),
      MOVE_MAT_GUID: Joi.string().max(64),
      MOVE_MAT_VERSION: Joi.string().max(20),
      FUNC_AREA: Joi.string().max(8),
      TR_PART_BA: Joi.string().max(8),
      PAR_COMPCO: Joi.string().max(8),
      DELIV_NUMB: Joi.string().max(20),
      DELIV_ITEM: Joi.string().max(12),
      NB_SLIPS: Joi.string().max(6),
      NB_SLIPSX: Joi.string().max(2),
      GR_RCPTX: Joi.string().max(2),
      UNLOAD_PTX: Joi.string().max(2),
      SPEC_MVMT: Joi.string().max(2),
      GRANT_NBR: Joi.string().max(40),
      CMMT_ITEM_LONG: Joi.string().max(48),
      FUNC_AREA_LONG: Joi.string().max(32),
      LINE_ID: Joi.string().max(12),
      PARENT_ID: Joi.string().max(12),
      LINE_DEPTH: Joi.string().max(4),
      QUANTITY: Joi.string().max(7),
      BASE_UOM: Joi.string().max(6),
      LONGNUM: Joi.string().max(80),
      BUDGET_PERIOD: Joi.string().max(20),
      EARMARKED_NUMBER: Joi.string().max(20),
      EARMARKED_ITEM: Joi.string().max(6),
    }),
    Serials: Joi.array().items({
      SERIALNO: Joi.string().max(36),
      UII: Joi.string().max(144),
    }),
  }),
  KanbanStatusChange: Joi.object({
    ORDERID: Joi.string().max(12),
    KANBANIDNUMBER: Joi.string().max(10),
    NEXTSTATUS: Joi.string().max(1),
  }),
});

SchemaJoi.MatDocUpdate = Joi.object({
  Header: Joi.object({
    MaterialDocumentNumber: Joi.string().min(10).max(10).required(),
    MaterialDocumentYear: Joi.string().min(4).max(4).required(),
    HeaderText: Joi.string().max(25).empty(null).allow(null, "")
  }),
  Items: Joi.array().items({
    ItemNumber: Joi.string().min(4).max(4).required(),
    UnloadingPoint: Joi.string().max(25).empty(null).allow(null, ""),
    ItemText: Joi.string().max(50).empty(null).allow(null, "")
  })
});

module.exports = SchemaJoi;