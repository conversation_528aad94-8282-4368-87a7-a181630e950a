# SAP Goods Movement Create - Field Level Mapping Documentation

## Overview
This document provides comprehensive field-level mapping information for the SAP Goods Movement Create functionality, detailing the transformation from Microservice API fields to SAP BAPI fields.

## Business Functionality
The `/GoodsMovementCreate` route serves as a payload template provider for SAP Goods Movement creation. It provides different payload formats (JSON, XML, XSD, TEXT) that clients can use to understand the required data structure for creating goods movements in SAP.

## SAP BAPI Names Used
1. **BAPI_GOODSMVT_CREATE** - Creates goods movement documents in SAP
2. **BAPI_GOODSMVT_GETDETAIL** - Retrieves existing goods movement details
3. **BAPI_KANBAN_CHANGESTATUS1** - Changes Kanban status
4. **BAPI_TRANSACTION_COMMIT** - Commits the transaction
5. **BAPI_MESSAGE_GETDETAIL** - Gets detailed error messages
6. **RFC_READ_TABLE** - Reads SAP table data (PKPS, T063 tables)
7. **ZSD_DELIVERY_UPDATE_ASYNC** - Custom RFC for document updates

## Input Structure Transformation Pipeline

### Stage 1: Content-Type Detection
- Supports: application/json, text/json, application/xml, text/xml
- Validates content-type and rejects unsupported formats

### Stage 2: XML to JSON Transformation
- Converts XML payload to JSON if content-type is XML
- Handles array normalization for Items and Serials
- Uses xml2js parser with specific options

### Stage 3: Payload Validation
- Joi schema validation with comprehensive field validation rules
- Field length validation, data type validation, required field validation
- Date format validation (YYYYMMDD), reference preservation for error tracking

### Stage 4: BAPI Structure Mapping
- Structure initialization and header transformation
- Items transformation with field formatting
- Serial number processing with item linkage
- Kanban integration with lookup functionality

## Header Fields Mapping

| Microservice Field | Arrow | BAPI Field | Transformation Logic | Validation |
|---|---|---|---|---|
| Header.GOODSMVT_CODE | → Extract → | GOODSMVT_CODE.GM_CODE | Special: Extracted and moved to separate structure | Required, Max 2 chars |
| Header.PSTNG_DATE | → Direct → | GOODSMVT_HEADER.PSTNG_DATE | Direct mapping, no transformation | Date format YYYYMMDD |
| Header.DOC_DATE | → Direct → | GOODSMVT_HEADER.DOC_DATE | Direct mapping, no transformation | Date format YYYYMMDD |
| Header.REF_DOC_NO | → Direct → | GOODSMVT_HEADER.REF_DOC_NO | Direct mapping, no transformation | Max 32 chars |
| Header.BILL_OF_LADING | → Direct → | GOODSMVT_HEADER.BILL_OF_LADING | Direct mapping, no transformation | Max 32 chars |
| Header.GR_GI_SLIP_NO | → Direct → | GOODSMVT_HEADER.GR_GI_SLIP_NO | Direct mapping, no transformation | Max 20 chars |
| Header.PR_UNAME | → Direct → | GOODSMVT_HEADER.PR_UNAME | Direct mapping, no transformation | Max 24 chars |
| Header.HEADER_TXT | → Direct → | GOODSMVT_HEADER.HEADER_TXT | Direct mapping, no transformation | Max 50 chars |
| Header.VER_GR_GI_SLIP | → Direct → | GOODSMVT_HEADER.VER_GR_GI_SLIP | Direct mapping, no transformation | Max 2 chars |
| Header.VER_GR_GI_SLIPX | → Direct → | GOODSMVT_HEADER.VER_GR_GI_SLIPX | Direct mapping, no transformation | Max 2 chars |
| Header.EXT_WMS | → Direct → | GOODSMVT_HEADER.EXT_WMS | Direct mapping, no transformation | Max 2 chars |
| Header.REF_DOC_NO_LONG | → Direct → | GOODSMVT_HEADER.REF_DOC_NO_LONG | Direct mapping, no transformation | Max 70 chars |
| Header.BILL_OF_LADING_LONG | → Direct → | GOODSMVT_HEADER.BILL_OF_LADING_LONG | Direct mapping, no transformation | Max 70 chars |
| Header.BAR_CODE | → Direct → | GOODSMVT_HEADER.BAR_CODE | Direct mapping, no transformation | Max 80 chars |

## Item Fields Mapping with Transformations

| Microservice Field | Arrow | BAPI Field | Transformation Logic | Code Example |
|---|---|---|---|---|
| Items[].Item.MATERIAL | → Transform → | GOODSMVT_ITEM[].MATERIAL | Zero-pad to 18 digits if numeric | "123" → "000000000000000123" |
| Items[].Item.PLANT | → Direct → | GOODSMVT_ITEM[].PLANT | Direct mapping, no transformation | "1000" → "1000" |
| Items[].Item.STGE_LOC | → Direct → | GOODSMVT_ITEM[].STGE_LOC | Direct mapping, no transformation | "0001" → "0001" |
| Items[].Item.BATCH | → Direct → | GOODSMVT_ITEM[].BATCH | Direct mapping, no transformation | "BATCH001" → "BATCH001" |
| Items[].Item.MOVE_TYPE | → Direct → | GOODSMVT_ITEM[].MOVE_TYPE | Direct mapping, no transformation | "101" → "101" |
| Items[].Item.STCK_TYPE | → Direct → | GOODSMVT_ITEM[].STCK_TYPE | Direct mapping, no transformation | "01" → "01" |
| Items[].Item.SPEC_STOCK | → Direct → | GOODSMVT_ITEM[].SPEC_STOCK | Direct mapping, no transformation | "E" → "E" |
| Items[].Item.VENDOR | → Direct → | GOODSMVT_ITEM[].VENDOR | Direct mapping, no transformation | "1000000123" → "1000000123" |
| Items[].Item.CUSTOMER | → Direct → | GOODSMVT_ITEM[].CUSTOMER | Direct mapping, no transformation | "2000000456" → "2000000456" |
| Items[].Item.SALES_ORD | → Direct → | GOODSMVT_ITEM[].SALES_ORD | Direct mapping, no transformation | "1000000123" → "1000000123" |
| Items[].Item.S_ORD_ITEM | → Direct → | GOODSMVT_ITEM[].S_ORD_ITEM | Direct mapping, no transformation | "000010" → "000010" |
| Items[].Item.SCHED_LINE | → Direct → | GOODSMVT_ITEM[].SCHED_LINE | Direct mapping, no transformation | "0001" → "0001" |
| Items[].Item.VAL_TYPE | → Direct → | GOODSMVT_ITEM[].VAL_TYPE | Direct mapping, no transformation | "STD" → "STD" |
| Items[].Item.ENTRY_QNT | → Direct → | GOODSMVT_ITEM[].ENTRY_QNT | Direct mapping, no transformation | "10.5" → "10.5" |
| Items[].Item.ENTRY_UOM | → Direct → | GOODSMVT_ITEM[].ENTRY_UOM | Direct mapping, no transformation | "EA" → "EA" |
| Items[].Item.ENTRY_UOM_ISO | → Direct → | GOODSMVT_ITEM[].ENTRY_UOM_ISO | Direct mapping, no transformation | "PCE" → "PCE" |
| Items[].Item.PO_PR_QNT | → Direct → | GOODSMVT_ITEM[].PO_PR_QNT | Direct mapping, no transformation | "10.000" → "10.000" |
| Items[].Item.ORDERPR_UN | → Direct → | GOODSMVT_ITEM[].ORDERPR_UN | Direct mapping, no transformation | "EA" → "EA" |
| Items[].Item.ORDERPR_UN_ISO | → Direct → | GOODSMVT_ITEM[].ORDERPR_UN_ISO | Direct mapping, no transformation | "PCE" → "PCE" |
| Items[].Item.PO_NUMBER | → Direct → | GOODSMVT_ITEM[].PO_NUMBER | Direct mapping, no transformation | "4500000123" → "4500000123" |
| Items[].Item.PO_ITEM | → Direct → | GOODSMVT_ITEM[].PO_ITEM | Direct mapping, no transformation | "00010" → "00010" |
| Items[].Item.SHIPPING | → Direct → | GOODSMVT_ITEM[].SHIPPING | Direct mapping, no transformation | "01" → "01" |
| Items[].Item.COMP_SHIP | → Direct → | GOODSMVT_ITEM[].COMP_SHIP | Direct mapping, no transformation | "X" → "X" |
| Items[].Item.NO_MORE_GR | → Direct → | GOODSMVT_ITEM[].NO_MORE_GR | Direct mapping, no transformation | "X" → "X" |
| Items[].Item.ITEM_TEXT | → Direct → | GOODSMVT_ITEM[].ITEM_TEXT | Direct mapping, no transformation | "Test Item" → "Test Item" |
| Items[].Item.GR_RCPT | → Direct → | GOODSMVT_ITEM[].GR_RCPT | Direct mapping, no transformation | "John Doe" → "John Doe" |
| Items[].Item.UNLOAD_PT | → Direct → | GOODSMVT_ITEM[].UNLOAD_PT | Direct mapping, no transformation | "Dock 1" → "Dock 1" |
| Items[].Item.COSTCENTER | → Transform → | GOODSMVT_ITEM[].COSTCENTER | Zero-pad to 10 digits if numeric | "1000" → "0000001000" |
| Items[].Item.ORDERID | → Direct → | GOODSMVT_ITEM[].ORDERID | Direct mapping, no transformation | "************" → "************" |
| Items[].Item.ORDER_ITNO | → Direct → | GOODSMVT_ITEM[].ORDER_ITNO | Direct mapping, no transformation | "0010" → "0010" |
| Items[].Item.CALC_MOTIVE | → Direct → | GOODSMVT_ITEM[].CALC_MOTIVE | Direct mapping, no transformation | "01" → "01" |
| Items[].Item.ASSET_NO | → Direct → | GOODSMVT_ITEM[].ASSET_NO | Direct mapping, no transformation | "100000123" → "100000123" |
| Items[].Item.SUB_NUMBER | → Direct → | GOODSMVT_ITEM[].SUB_NUMBER | Direct mapping, no transformation | "0001" → "0001" |
| Items[].Item.RESERV_NO | → Direct → | GOODSMVT_ITEM[].RESERV_NO | Direct mapping, no transformation | "0000123456" → "0000123456" |
| Items[].Item.RES_ITEM | → Direct → | GOODSMVT_ITEM[].RES_ITEM | Direct mapping, no transformation | "0001" → "0001" |
| Items[].Item.RES_TYPE | → Direct → | GOODSMVT_ITEM[].RES_TYPE | Direct mapping, no transformation | "AR" → "AR" |
| Items[].Item.WITHDRAWN | → Direct → | GOODSMVT_ITEM[].WITHDRAWN | Direct mapping, no transformation | "X" → "X" |
| Items[].Item.MOVE_MAT | → Direct → | GOODSMVT_ITEM[].MOVE_MAT | Direct mapping, no transformation | "MAT456" → "MAT456" |
| Items[].Item.MOVE_PLANT | → Direct → | GOODSMVT_ITEM[].MOVE_PLANT | Direct mapping, no transformation | "2000" → "2000" |
| Items[].Item.MOVE_STLOC | → Direct → | GOODSMVT_ITEM[].MOVE_STLOC | Direct mapping, no transformation | "0002" → "0002" |
| Items[].Item.MOVE_BATCH | → Direct → | GOODSMVT_ITEM[].MOVE_BATCH | Direct mapping, no transformation | "BATCH002" → "BATCH002" |
| Items[].Item.MOVE_VAL_TYPE | → Direct → | GOODSMVT_ITEM[].MOVE_VAL_TYPE | Direct mapping, no transformation | "STD" → "STD" |
| Items[].Item.MVT_IND | → Direct → | GOODSMVT_ITEM[].MVT_IND | Direct mapping, no transformation | "B" → "B" |
| Items[].Item.MOVE_REAS | → Direct → | GOODSMVT_ITEM[].MOVE_REAS | Direct mapping, no transformation | "0001" → "0001" |
| Items[].Item.WBS_ELEM | → Direct → | GOODSMVT_ITEM[].WBS_ELEM | Direct mapping, no transformation | "P-12345-1.1.1" → "P-12345-1.1.1" |
| Items[].Item.NETWORK | → Direct → | GOODSMVT_ITEM[].NETWORK | Direct mapping, no transformation | "************" → "************" |
| Items[].Item.ACTIVITY | → Direct → | GOODSMVT_ITEM[].ACTIVITY | Direct mapping, no transformation | "0010" → "0010" |
| Items[].Item.PROFIT_CTR | → Direct → | GOODSMVT_ITEM[].PROFIT_CTR | Direct mapping, no transformation | "PC1000" → "PC1000" |
| Items[].Item.GL_ACCOUNT | → Direct → | GOODSMVT_ITEM[].GL_ACCOUNT | Direct mapping, no transformation | "**********" → "**********" |

## Serial Number Fields Mapping

| Microservice Field | Arrow | BAPI Field | Transformation Logic | Code Example |
|---|---|---|---|---|
| Items[].Serials[].SERIALNO | → Direct → | GOODSMVT_SERIALNUMBER[].SERIALNO | Direct mapping, no transformation | "SN123456" → "SN123456" |
| Items[].Serials[].UII | → Direct → | GOODSMVT_SERIALNUMBER[].UII | Direct mapping, no transformation | "UII789012" → "UII789012" |
| Auto-generated | → Generate → | GOODSMVT_SERIALNUMBER[].MATDOC_ITM | Auto-increment item counter, zero-pad to 4 digits | Item 1 → "0001" |

## Kanban Fields Mapping

| Microservice Field | Arrow | BAPI Field | Transformation Logic | Code Example |
|---|---|---|---|---|
| KanbanStatusChange.ORDERID | → Lookup → | KanbanStatusChange.KANBANIDNUMBER | RFC_READ_TABLE query on PKPS table | "************" → "**********" |
| KanbanStatusChange.ORDERID | → Direct → | KanbanStatusChange.ORDERID | Direct mapping, preserved for reference | "************" → "************" |
| KanbanStatusChange.KANBANIDNUMBER | → Direct → | KanbanStatusChange.KANBANIDNUMBER | Direct mapping if provided, else lookup | "**********" → "**********" |
| KanbanStatusChange.NEXTSTATUS | → Direct → | KanbanStatusChange.NEXTSTATUS | Direct mapping, no transformation | "3" → "3" |

## Transformation Logic Code Examples

### 1. Material Number Transformation
```javascript
// Input: Items[].Item.MATERIAL
if (!isNaN(e.Item.MATERIAL)) {
  e.Item.MATERIAL = ("000000000000000000" + e.Item.MATERIAL).slice(-18);
}
// Examples:
// "123" → "000000000000000123"
// "ABC123" → "ABC123" (no change for non-numeric)
```

### 2. Cost Center Transformation
```javascript
// Input: Items[].Item.COSTCENTER
if (!isNaN(e.Item.COSTCENTER)) {
  e.Item.COSTCENTER = ("0000000000" + e.Item.COSTCENTER).slice(-10);
}
// Examples:
// "1000" → "0000001000"
// "CC1000" → "CC1000" (no change for non-numeric)
```

### 3. Serial Number Item Link Generation
```javascript
// Auto-generated for each serial number
let MatDocItem = 0;
bodyPayload.Items.forEach((e) => {
  MatDocItem += 1;
  e.Serials.forEach((s) => {
    s.MATDOC_ITM = ("0000" + String(MatDocItem)).slice(-4);
  });
});
// Examples:
// Item 1 → "0001"
// Item 2 → "0002"
```

### 4. GOODSMVT_CODE Extraction
```javascript
// Input: Header.GOODSMVT_CODE
exporting.GOODSMVT_CODE.GM_CODE = bodyPayload.Header.GOODSMVT_CODE;
delete bodyPayload.Header.GOODSMVT_CODE;
exporting.GOODSMVT_HEADER = bodyPayload.Header;
// Result: Moves GOODSMVT_CODE to separate structure
```

### 5. Kanban ID Lookup
```javascript
// Input: KanbanStatusChange.ORDERID
if (bodyPayload.KanbanStatusChange.ORDERID) {
  bodyPayload.KanbanStatusChange.KANBANIDNUMBER =
    await this.production_Order_KanbanId(
      bodyPayload.KanbanStatusChange.ORDERID
    );
}
// RFC_READ_TABLE query on PKPS table:
// QUERY_TABLE: "PKPS"
// OPTIONS: [`AUFNR = '${OrderId}'`]
```

## Field Validation Rules

### Required Fields
- Header.GOODSMVT_CODE - Mandatory for all goods movements

### Format Validations
- Date Fields: Must be in YYYYMMDD format
- Numeric Fields: Validated for proper numeric format before padding
- String Fields: Length validation based on SAP field definitions

### Business Logic Validations
- Movement Type: Must be valid SAP movement type
- Plant/Storage Location: Must exist in SAP master data
- Material Number: Must be valid material master
- Cost Center: Must be active cost center if provided

## Error Handling

### XML Parsing Errors
- Handles XML parsing errors with detailed error messages
- Returns structured error response with TYPE "E"

### Validation Errors
- Joi validation errors with field-specific messages
- Preserves reference document information for error tracking

### Content-Type Based Error Formatting
- Formats errors based on request content-type (JSON/XML)
- Maintains consistency in error response structure

## Complete BAPI Structure Output Example

```javascript
{
  GOODSMVT_CODE: {
    GM_CODE: "01"  // Goods Movement Code
  },
  GOODSMVT_HEADER: {
    PSTNG_DATE: "20231215",
    DOC_DATE: "20231215",
    REF_DOC_NO: "REF123",
    HEADER_TXT: "Test Movement"
    // ... all other header fields
  },
  GOODSMVT_ITEM: [
    {
      MATERIAL: "000000000000000123",
      PLANT: "1000",
      STGE_LOC: "0001",
      MOVE_TYPE: "101",
      ENTRY_QNT: "10",
      ENTRY_UOM: "EA",
      COSTCENTER: "0000001000"
      // ... all other item fields
    }
  ],
  GOODSMVT_SERIALNUMBER: [
    {
      MATDOC_ITM: "0001",
      SERIALNO: "SN123456",
      UII: "UII789012"
    }
  ]
}
```

This comprehensive mapping ensures proper data transformation from the REST API payload to SAP BAPI-compatible structure while maintaining data integrity and applying necessary formatting rules.
